/* 列表options */
export const listOptions = [
  { label: '应收单号', key: 'code', listKey: 'code', type: 'select' },
  { label: '收款账户', key: 'bank_account_id', listKey: 'bank_account', type: 'select' },
  { label: '关联发票', key: 'invoice_code', listKey: 'invoice', type: 'select' },
  { label: '客户', key: 'customer_id', listKey: 'customer', type: 'select' },
  { label: '应收日期', key: 'receivable_time', listKey: '', type: 'date' },
  { label: '币种', key: 'currency', listKey: 'currency', type: 'select' },
  { label: '创建人', key: 'gen_user_id', listKey: 'gen_user', type: 'select' },
  { label: '创建日期', key: 'gen_time', listKey: '', type: 'date' },
];

/* 表格配置 */
const headerBaseConfig = { visible: true, type: 'text', width: '112px', sort: false, pinned: false };
export const tableHeader = [
  {
    label: '应收单号',
    key: 'code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '收款账户',
    key: 'bank_account',
    ...headerBaseConfig,
    width: '140px',
    type: 'template',
    templateName: 'bank_account',
  },
  {
    label: '关联发票',
    key: 'invoice_code',
    ...headerBaseConfig,
    type: 'template',
    width: '140px',
    templateName: 'invoice_code',
  },
  {
    label: '发票税率(%)',
    key: 'invoice_tax_rate',
    ...headerBaseConfig,
    type: 'text',
    width: '140px',
    templateName: 'invoice_tax_rate',
  },
  {
    label: '发票类型',
    key: 'invoice_type_name',
    ...headerBaseConfig,
    type: 'template',
    width: '140px',
    templateName: 'invoice_type_name',
  },
  {
    label: '核销状态',
    key: 'verification_status_name',
    ...headerBaseConfig,
    style: getStatusColorStyle.bind(this),
  },
  {
    label: '核销金额',
    key: 'verification_price',
    ...headerBaseConfig,
  },
  {
    label: '客户',
    key: 'customer_name',
    ...headerBaseConfig,
  },
  {
    label: '关联单据',
    key: 'order_code',
    ...headerBaseConfig,
    type: 'template',
    templateName: 'order_code',
  },
  {
    label: '关联款号',
    key: 'style_code',
    ...headerBaseConfig,
    width: '140px',
    type: 'template',
    templateName: 'style_code',
  },
  {
    label: '应收金额',
    key: 'total_money',
    ...headerBaseConfig,
  },
  {
    label: '应收金额(本币)',
    key: 'total_money_local',
    ...headerBaseConfig,
  },
  {
    label: '应收日期',
    key: 'receivable_dates',
    ...headerBaseConfig,
    type: 'text',
    width: '140px',
    templateName: 'receivable_date',
  },
  {
    label: '币种',
    key: 'currency',
    ...headerBaseConfig,
  },
  {
    label: '创建人/创建时间',
    key: 'gen_info',
    ...headerBaseConfig,
    width: '160px',
    type: 'template',
    templateName: 'gen_info',
  },
  {
    label: '收款状态',
    key: 'collection_status_name',
    ...headerBaseConfig,
  },
  {
    label: '状态',
    key: 'status_name',
    ...headerBaseConfig,
    style: getStatusColorStyle.bind(this),
  },
];

function getStatusColorStyle(item: any) {
  const styleObj: any = {};
  switch (item.order_status) {
    case 1: // 待提交
      styleObj.color = '#138AFF';
      break;
    case 2: // 待审核
    case 9: // 修改待审核
      styleObj.color = '#FB6401';
      break;
    case 4: // 待修改
    case 10: // 修改未通过
      styleObj.color = '#FF4A1D';
      break;
    case 8: // 已取消
      styleObj.color = '#97999C';
      break;
  }
  return styleObj;
}
