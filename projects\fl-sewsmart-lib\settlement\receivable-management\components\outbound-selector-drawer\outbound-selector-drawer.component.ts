import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { endOfDay, format, startOfDay } from 'date-fns';
import { FlcTableComponent } from 'fl-common-lib';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { ReceivableService } from '../../receivable-service';
import { initOutboundSelectorHeaders } from './outbound-selector-drawer.config';
import { OutboundTypeEnum } from '../../models/receivable-detail.enum';
import { OutboundList } from '../../models/receivable-detail.interface';

@Component({
  selector: 'flss-outbound-selector-drawer',
  templateUrl: './outbound-selector-drawer.component.html',
  styleUrls: ['./outbound-selector-drawer.component.scss'],
})
export class OutboundSelectorDrawerComponent implements OnInit {
  @ViewChild(FlcTableComponent) flcTable!: FlcTableComponent;
  // @Input() factory: { factory_code?: string; factory_name?: string } = {};
  @Input() disabledList: number[] = [];
  @Input() currentCustomerId?: string; // 当前选择的客户ID
  @Input() currentCustomerName?: string
  @Input() currentCurrencyId?: string; // 当前选择的币种ID
  @Input() hasExistingData: boolean = false; // 是否已有数据
  @Input() historySelectedIds: string[] = []; // 已选择的出库单ID列表
  searchData: any = {
    code: null,
    order_code: null,
    style_code: null,
    customer_id: null,
  };
  optionsList: any = {
    outbound_rel_order: [],
    outbound_type: [],
    outbound_code: [],
    customer: [],
    style_code: [],
  };
  inStockTypeEnum = OutboundTypeEnum;

  listOfData: OutboundList[] = [];
  constructor(
    private _drawerRef: NzDrawerRef,
    private _service: ReceivableService,
    private _noticeService: NzNotificationService
  ) { }

  // 设置表头信息
  tableHeader = initOutboundSelectorHeaders();
  // 表格配置项
  tableConfig = {
    version: '1.0.1',
    tableName: 'materialOrderSelectorTable1',
    translateName: '',
    dataList: <OutboundList[]>[], //表格数据
    hasCheckbox: true, //是否显示选中功能
    detailBtn: false,
    count: 0, //数据总数量
    pageIndex: 1, //当前页码
    pageSize: 20, //当前每页数量
    clearSelect: false,
    height: 500,
    loading: false,
    uniqueId: 'id',
  };

  ngOnInit() {
    // 如果有客户ID，设置到搜索条件中
    if (this.currentCustomerId) {
      this.searchData.customer_id = this.currentCustomerName;
    }

    this.getList();
    this.getOptions();
  }

  private getList() {
    const _params = this.handleWhere();
    this._service.getOutboundList(_params).subscribe((res) => {
      this.listOfData = res.data.data;
      this.tableConfig.count = res.data.total;
      this.tableConfig.dataList = res.data.data;
      this.tableConfig = { ...this.tableConfig };
    });
  }


  private getOptions() {
    this._service.getOutboundListOptions().subscribe((res) => {
      this.optionsList = res.data;
    });
  }

  private handleWhere() {
    const _where: any = {
      ...this.searchData,
      customer_id: String(this.searchData.customer_id),
      page: this.tableConfig.pageIndex,
      page_size: this.tableConfig.pageSize,
      // factory_code: this.factory.factory_code,
    };
    if (this.searchData.outbound_time?.length) {
      const startTime = Number(format(startOfDay(this.searchData.outbound_time[0]), 'T'));
      const endTime = Number(format(endOfDay(this.searchData.outbound_time[1]), 'T'));
      _where.outbound_time_start = startTime;
      _where.outbound_time_end = endTime;
    } else {
      _where.outbound_time_start = null;
      _where.outbound_time_end = null;
    }
    return _where;
  }

  onSearch() {
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  handleOk() {
    if (this.selectData.length === 0) {
      this._noticeService.warning('请选择出库单', '');
      return;
    }

    if (this.validateCustomerAndCurrency(this.selectData)) {
      this._drawerRef.close(this.selectData);
    }
  }

  onSelect(item: any) {
    if (this.validateCustomerAndCurrency([item])) {
      this._drawerRef.close([item]);
    }
  }

  /**
   * 验证客户和币种是否一致
   */
  private validateCustomerAndCurrency(selectedData: OutboundList[]): boolean {
    if (!selectedData || selectedData.length === 0) {
      return false;
    }

    // 作为前置判断 - 检查选中的订单是否为同客户同币种
    const reference = selectedData[0];
    const fliterList = selectedData?.filter((item: any) => {
      // 检查客户ID是否不同
      if (item?.customer_id !== reference?.customer_id) {
        return true;
      }

      // 检查币种ID是否不同 - 正确处理undefined的情况
      const itemCurrencyId = item?.orders?.[0]?.currency_id;
      const referenceCurrencyId = reference?.orders?.[0]?.currency_id;

      // 如果两个都是undefined，认为是相同的；如果一个是undefined另一个不是，认为是不同的
      if (itemCurrencyId !== referenceCurrencyId) {
        return true;
      }

      return false;
    });

    if (fliterList?.length) {
      this._noticeService.warning('请选择同客户同币种的订单哦', '');
      return false;
    }

    // 检查是否包含历史已选择的出库单ID
    for (const item of selectedData) {
      if (item.id && this.historySelectedIds.includes(item.id)) {
        this._noticeService.warning(`当前已存在${item.code}哦`, '');
        return false;
      }
    }

    // 如果没有现有数据，直接通过验证
    if (!this.hasExistingData) {
      return true;
    }

    // 检查所有选中的出库单是否与当前客户和币种一致
    for (const item of selectedData) {
      // 检查客户是否一致
      if (this.currentCustomerId && item.customer_id !== this.currentCustomerId) {
        this._noticeService.warning('请选择同客户同币种的订单哦', '');
        return false;
      }

      // 检查币种是否一致 - 从orders数组中获取币种信息
      if (this.currentCurrencyId && item.orders && item.orders.length > 0) {
        for (const order of item.orders) {
          if (order.currency_id && order.currency_id !== this.currentCurrencyId) {
            this._noticeService.warning('请选择同客户同币种的订单哦', '');
            return false;
          }
        }
      }
    }

    return true;
  }

  onClear() {
    this.flcTable.clearAllSelected();
  }

  /**
   * 页码变化
   * @param page
   */
  indexChanges(page: number): void {
    this.tableConfig.pageIndex = page;
    this.tableConfig = { ...this.tableConfig };
    this.getList();
    // this.getPageOfList(this.tableConfig.pageIndex, this.tableConfig.pageSize);
  }

  /**
   * 页数变化
   * @param size
   */
  sizeChanges(size: number): void {
    this.tableConfig.pageSize = size;
    this.tableConfig = { ...this.tableConfig };
    this.getList();
    // this.tableConfig.pageIndex = 1;
    // this.getPageOfList(this.tableConfig.pageIndex, this.tableConfig.pageSize);
  }

  /**
   * 复选框勾选变化
   * @param e
   */
  selectData: OutboundList[] = [];
  getCount(e: { count: number; list: OutboundList[] }) {
    this.selectData = [...(e?.list ?? [])];
  }

  /**
   * 重置列表
   */
  resetList(): void {
    this.searchData = {
      code: null,
      outbound_type: null,
      outbound_time: null,
    };
    this.getList();
  }

  /**
   * 获取关联单据显示文本
   * @param item 出库单数据
   * @returns 关联单据字符串，多个用"、"分隔，无关联订单时显示"-"
   */
  getRelatedDocuments(item: OutboundList): string {
    if (!item.orders || item.orders.length === 0) {
      return '-';
    }

    // 提取所有非空的订单编码
    const orderCodes = item.orders
      .map(order => order.order_code)
      .filter(code => code && code.trim() !== '');

    // 去重并排序
    const uniqueOrderCodes = [...new Set(orderCodes)];

    return uniqueOrderCodes.length > 0 ? uniqueOrderCodes.join('、') : '-';
  }

  /**
   * 获取关联款号显示文本
   * @param item 出库单数据
   * @returns 关联款号字符串，多个用"、"分隔
   */
  getRelatedStyleCodes(item: OutboundList): string {
    if (!item.orders || item.orders.length === 0) {
      return '';
    }

    // 提取所有非空的款号
    const styleCodes = item.orders
      .map(order => order.style_code)
      .filter(code => code && code.trim() !== '');

    // 去重并排序
    const uniqueStyleCodes = [...new Set(styleCodes)];

    return uniqueStyleCodes.join('、');
  }
}
