import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { ReceivableService } from '../receivable-service';
import { FlcModalService, FlcTableComponent, resizable } from 'fl-common-lib';
import { endOfDay, format, startOfDay } from 'date-fns';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { listOptions, tableHeader } from '../models/config';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ReturnModelComponent } from '../../components/return-model/return-model.component';

@Component({
  selector: 'flss-receivable-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss'],
})
@resizable()
export class ReceivableListComponent implements OnInit {
  @ViewChild('searchHeader') hearderComponent?: ElementRef;
  @ViewChild('tableRef') tableRef!: FlcTableComponent;

  checkedInfo!: { count: number; list: any[] };
  checkData = [
    {
      label: '全部',
      value: 0,
      key: 'all',
      isTotalItem: true,
      amount: '0',
    },
    {
      label: '待提交',
      value: 11,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '待审核',
      value: 12,
      key: 'tobe_inbound',
      amount: '0',
    },
    {
      label: '审核通过',
      value: 13,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '待修改',
      value: 14,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '部分核销',
      value: 22,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '部分收款',
      value: 32,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '待核销',
      value: 21,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '已核销',
      value: 23,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '待收款',
      value: 31,
      key: 'inbound',
      amount: '0',
    },
    {
      label: '已收款',
      value: 33,
      key: 'inbound',
      amount: '0',
    }
  ];

  searchList = listOptions;
  tableHeader = tableHeader;
  searchData: any = {};

  tableConfig: any = {
    tableName: 'receivable-list',
    dataList: [],
    count: null,
    height: 500,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    actionWidth: '100px',
    settingBtnPos: 'start',
    version: '1.0.0',
    hasCheckbox: true,
  };

  isExporting = false;
  env_project = 'sewsmart'; //默认系统环境变量
  userActions: any = [];
  optionMaps = {};

  constructor(
    @Inject('environment') env: any,
    private _service: ReceivableService,
    private _router: Router,
    private _msg: NzMessageService,
    private _activeRoute: ActivatedRoute,
    private _notice: NzNotificationService,
    private _flcModalService: FlcModalService,
    private _modalService: NzModalService // private _nzModalRef: NzModalRef
  ) {
    env.project && (this.env_project = env.project);
  }

  ngOnInit() {
    (this as any).addResizePageListener();
    this.userActions = this._service.getUserActions();
    this.getList();
    this.getOptions();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  resizePage() {
    const headerHeight = this.hearderComponent?.nativeElement?.offsetHeight ?? 32;
    this.tableConfig.height = window.innerHeight - headerHeight - 140;
    this.tableConfig = { ...this.tableConfig };
  }

  /**
   * 获取列表
   * @param  {} reset=false // 是否重置
   */
  getList(reset?: boolean): Promise<void> {
    return new Promise((resolve) => {
      this.tableConfig.loading = true;
      if (reset) {
        this.resetSearch();
        this.tableConfig.pageIndex = 1;
      }
      const data = {
        ...this.handleWhere(),
        size: this.tableConfig.pageSize,
        page: this.tableConfig.pageIndex,
      };
      this._service.getList(data).subscribe((res: any) => {
        this.tableConfig.dataList = res.data.data;
        this.tableConfig.count = res.data.total;
        this.tableConfig.loading = false;
        this.tableConfig = { ...this.tableConfig };

        res.data.statics?.forEach((item: any) => {
          const target = this.checkData.find((e) => e.value === item.status);
          if (target) {
            target.amount = item.count;
          }
        });
        resolve(); // 确保数据加载完成后才resolve
      });
    });
  }

  // TODO 处理真实参数
  resetSearch() {
    this.searchData = {
      ...this.searchData,
      code: null, // 收款单号
      bank_account_id: null, // 收款账户id
      invoice_code: null, // 关联发票
      customer_id: null, // 客户
      currency: null, // 币种
      gen_user_id: null, // 创建人
      status: null, // 应收单状态
      receivable_time: [], // 应收日期
      gen_time: [], // 创建日期
    };
  }

  handleWhere() {
    const where: any = {};
    // TODO 调整表真实参数
    Object.entries(this.searchData).forEach((item: any) => {
      if (item[1]) {
        switch (item[0]) {
          case 'receivable_time':
            where['receivable_time_start'] = item[1][0] ? Number(format(startOfDay(item[1][0]), 'T')) : null;
            where['receivable_time_end'] = item[1][1] ? Number(format(endOfDay(item[1][1]), 'T')) : null;
            break;
          case 'gen_time':
            where['gen_time_start'] = item[1][0] ? Number(format(startOfDay(item[1][0]), 'T')) : null;
            where['gen_time_end'] = item[1][1] ? Number(format(endOfDay(item[1][1]), 'T')) : null;
            break;
          default:
            where[item[0]] = item[1];
            break;
        }
      }
    });
    return where;
  }

  /** 
   * 获取下拉选项数据
   */
  getOptions() {
    this._service.getListOptions().subscribe((res: any) => {
      this.optionMaps = res.data;
    });
  }

  /**
   * 状态筛选查询
   */
  onStatusChanged(e: any) {
    this.searchData.status = e;
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  // 搜索条件切换
  modelChanges() {
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  /**
   * 页码改变
   * @param  {number} e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }
  /**
   * 页数改变
   * @param  {number} e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }

  selectedIds:number[] = [];
  // 选中的数量
  onSelectedCount(e: any) {
    this.checkedInfo = e;
    this.selectedIds = e.list.map((item: any) => item.id);
  }

  clearAllSelected() {
    this.tableRef.clearAllSelected();
    this.tableConfig = { ...this.tableConfig };
  }

  /**
   * 跳转详情
   * @param  {any} id
   */
  getDetails(id: any) {
    this._router.navigate(['../list', id], { relativeTo: this._activeRoute });
  }

  // 获取关联发票相关数据
  _getInvoices(data: any, key: string) {
    const _invoices = data?.invoices;
    const showArray = _invoices?.map((item: any) => {
      if (key == 'receivable_date') {
        const datetime = item?.[key];
        return datetime ? format(datetime, 'yyyy-MM-dd') : null;
      }
      return item?.[key];
    });
    return showArray?.join('、');
  }
  // 获取关联单据、关联款号
  _getCodesName(data: any, key: string) {
    const values = data?.[key] ?? []
    return values?.join('、')
  }

  onAudit(action: 'pass' | 'reject') {
    if (!this.checkedInfo.count) {
      this._notice.error('请至少选择一条数据', '');
      return false;
    }
    const _receivableCodes: string[] = [];
    const _ids: number[] = [];
    this.checkedInfo.list.forEach((item) => {
      item.status !== StatusEnum.pass ? _receivableCodes.push(item.code) : _ids.push(item.id);
    });
    if (_receivableCodes.length) {
      this._msg.warning(`应收单号${_receivableCodes.join('、')}不能操作${action === 'pass' ? '审核通过' : '退回修改'}`, { nzDuration: 10000 });
      return
    }
    if (!_ids.length) return;
    return action === 'pass' ? this._onPass(_ids) : this._onReject(_ids);
  }

  _onPass(_ids: number[]) {
    const ref = this._flcModalService.confirmCancel({ content: '确定审核通过' });
    ref.afterClose.subscribe((res) => {
      if (res) {
        this._service.batchPass(_ids).subscribe((res) => {
          if (res.data) {
            this._msg.success('审核通过');
            this.clearAllSelected();
            this.getOptions();
            this.getList(true);
          }
        });
      }
    });
  }

  _onReject(_ids: number[]) {
    this._modalService.create({
      nzContent: ReturnModelComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
      nzOnOk: (comp: any) => {
        const _value = comp.formGroup.getRawValue();
        this._service.batchReject(_ids, _value.reason).subscribe((result) => {
          if (result.data) {
            this._msg.success('退回成功');
            this.clearAllSelected();
            this.getOptions();
            this.getList(true);
          }
        });
      },
    });
  }

  // 批量开票
  onMakeInvoice() {
    if (!this.checkedInfo.count) {
      this._notice.error('请至少选择一条数据', '');
      return
    }
    // 过滤出待核销或部分核销的数据（核销状态：1 待核销，2 部分核销，3 已核销）
    const validList = this.checkedInfo.list?.filter((item: any) => item?.verification_status == 1 || item?.verification_status == 2)
    if (validList?.length <= 0) {
      this._notice.error('请选择待核销或部分核销的应收单', '');
      return
    }
    // 只传递符合条件的应收单ID
    const ids = validList.map((item) => item.id);
    this._service.batchSales(ids).subscribe((res) => {
      if (res.code == 200) {
        this._router.navigate(['/settlement/sales-invoice-management/list', 'new'], { queryParams: { id: res.data.id } });
      }
    });
  }

  // 批量收款
  async onMakeCollections() {
    if (!this.checkedInfo.count) {
      this._notice.error('请至少选择一条数据', '');
      return
    }
    await this.getList()
    this.checkedInfo.list = this.tableConfig.dataList.filter((item:any) => this.selectedIds.includes(item.id))
    console.log(this.checkedInfo)
    
    const errList = this.checkedInfo.list?.filter((item) => (item?.verification_status != 3 || item?.collection_status == 3))
    if (errList?.length) {
      this._notice.error('请选择“已核销”并且“待收款”或“部分收款”的应收单', '');
      return 
    }

    // 判断是不是同客户同币种
    const referenceData = this.checkedInfo.list?.[0] || null;
    const nextErrList = this.checkedInfo.list?.filter((item: any) => {
      return item?.customer_id !== referenceData?.customer_id || item?.currency !== referenceData?.currency
    })
    if (nextErrList?.length) {
      this._notice.error('请选择同客户同币种的应收单', '');
      return
    }

    const ids = this.checkedInfo.list.map((item) => item.id);
    this._service.baschToReceipt(ids).subscribe((res) => {
      if (res.code == 200) {
        this._router.navigate(['/settlement/receipt-management/list', 'new'], { queryParams: { id: res.data.id } });
      }
    });
  }

  // 新建
  onCreate() {
    this._router.navigate(['../list', 'add'], { relativeTo: this._activeRoute });
  }

  // 删除行
  deleteLine(data: any) {
    const status = data.status
    const id = data?.id ?? null
    if (status !== StatusEnum.wait_submit) {
      this._msg.warning(`该应收单不可操作删除`, { nzDuration: 10000 });
      return
    }
    const ref = this._flcModalService.confirmCancel({
      strongConfirm: true,
      content: '确定删除该应收单？',
    });
    ref.afterClose.subscribe((res: any) => {
      if (res) {
        this._service.baschDelete([id]).subscribe((res) => {
            if (res.code === 200) {
              this._msg.success('删除成功');
              this.clearAllSelected();
              this.getOptions();
              this.getList();
            }
          });
      }
    });
  }
}

enum StatusEnum {
  wait_submit = 1,
  pass = 2,
  reject = 3,
}
